{{ define "main" }}
<div class="main-container">
  <!-- Compact Icon Navigation -->
  <aside class="nav-sidebar">
    <nav class="navigation-menu">
      <div class="nav-items">
        <button id="nav-search" class="nav-item active" data-view="search" title="Search">
          <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
        </button>
        <button id="nav-personal" class="nav-item" data-view="personal" title="My Queues">
          <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 21H5V3H13V9H19Z"/>
          </svg>
        </button>
        <button id="nav-public" class="nav-item" data-view="public" title="Public Queues">
          <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,14L6,9L7.41,7.59L11,11.17L16.59,5.59L18,7L11,14Z"/>
          </svg>
        </button>
      </div>
    </nav>
  </aside>

  <!-- Main Content Area -->
  <div class="main-content">
    <!-- Content Views -->
    <div class="content-area">
    <!-- Search View -->
    <div id="search-view" class="view-content active">
      <div class="control-section">
        <!-- Queue Creation Mode Header -->
        <div id="queue-creation-header" class="queue-creation-header" style="display: none;">
          <div class="creation-mode-info">
            <div class="creation-mode-icon">+</div>
            <div class="creation-mode-text">
              <h3>Creating New Queue</h3>
              <p>Add videos to create your new queue</p>
            </div>
          </div>
          <div class="creation-mode-actions">
            <button id="cancel-creation-btn" class="creation-action-btn cancel-btn" title="Cancel">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Queue Creation Form -->
        <div id="queue-creation-form" class="queue-creation-form" style="display: none;">
          <div class="form-group">
            <input type="text" id="queue-title-input" class="queue-title-input" placeholder="Enter queue title..." />
            <div class="creation-step-hint" id="creation-step-hint">
              <span class="step-number">1</span>
              <span class="step-text">Enter a title, then search for videos below</span>
            </div>
          </div>
          <div class="form-actions">
            <button id="save-queue-btn" class="save-queue-btn" disabled>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17 3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V7L17 3M19 19H5V5H16.17L19 7.83V19M12 12C10.34 12 9 13.34 9 15S10.34 18 12 18 15 16.66 15 15 13.66 12 12 12M6 6H15V10H6V6Z"/>
              </svg>
              Save Queue
            </button>
          </div>
        </div>

        <!-- New Queue Button (when not in creation mode) -->
        <div class="new-queue-section" id="new-queue-section">
          <div class="new-queue-intro">
            <h2>Create Your Music Queue</h2>
            <p>Build custom playlists with your favorite YouTube videos</p>
          </div>
          <button id="new-queue-btn" class="new-queue-btn" title="Create New Queue">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
            Create New Queue
          </button>
        </div>

        <!-- Draft Queue Search (only visible in creation mode) -->
        <div class="draft-search-section" id="draft-search-section" style="display: none;">
          <div class="input-group">
            <div class="input-wrapper">
              <input type="text" id="unified-input" class="input-field" placeholder="Search for videos to add to your queue..." />
              <button id="clear-search-btn" class="input-clear-btn" title="Clear">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Draft Queue Display -->
        <div id="draft-queue-container" class="draft-queue-container" style="display: none;">
          <div class="draft-queue-header">
            <h4>Draft Queue <span id="draft-queue-count" class="queue-count">0</span></h4>
          </div>
          <div id="draft-queue-list" class="draft-queue-list"></div>
        </div>

        <!-- Search Results -->
        <div id="search-results" class="search-results empty"></div>
      </div>
    </div>

    <!-- Personal Queues View -->
    <div id="personal-view" class="view-content">
      <div class="queues-section">
        <div class="section-header">
          <h2>My Personal Queues</h2>
          <p>Your private queues that only you can see</p>
        </div>
        <div id="personal-queues-list" class="queues-list">
          <div class="queues-loading">
            <div class="loading-spinner"></div>
            <p>Loading your personal queues...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Public Queues View -->
    <div id="public-view" class="view-content">
      <div class="queues-section">
        <div class="section-header">
          <h2>Public Queues</h2>
          <p>Discover and play queues shared by the community</p>
        </div>
        <div class="queue-browser-controls">
          <select id="sort-select" class="sort-select">
            <option value="recent">Recently Updated</option>
            <option value="popular">Most Popular</option>
            <option value="newest">Newest</option>
            <option value="longest">Longest</option>
          </select>
        </div>
        <div id="public-queues-list" class="queues-list">
          <div class="queues-loading">
            <div class="loading-spinner"></div>
            <p>Discovering amazing queues from the community...</p>
          </div>
        </div>
      </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div id="message-container"></div>

<!-- Load JavaScript modules in dependency order -->
<script src="{{ "js/utils.js" | relURL }}"></script>
<script src="{{ "js/config.js" | relURL }}"></script>
<script src="{{ "js/firebase/core/firebase.js" | relURL }}"></script>
<script src="{{ "js/youtube-api.js" | relURL }}"></script>
<script src="{{ "js/search.js" | relURL }}"></script>

<!-- Firebase Models (loaded in dependency order) -->
<script src="{{ "js/firebase/models/firebase-model-base.js" | relURL }}"></script>
<script src="{{ "js/firebase/models/video-model.js" | relURL }}"></script>
<script src="{{ "js/firebase/models/queue-data-model.js" | relURL }}"></script>
<script src="{{ "js/firebase/models/queue-metadata-model.js" | relURL }}"></script>
<script src="{{ "js/firebase/models/public-queue-model.js" | relURL }}"></script>
<script src="{{ "js/firebase/models/personal-queue-model.js" | relURL }}"></script>
<script src="{{ "js/firebase/models/index.js" | relURL }}"></script>

<!-- Firebase Repositories -->
<script src="{{ "js/firebase/repositories/firebase-repositories.js" | relURL }}"></script>

<!-- Firebase Services -->
<script src="{{ "js/firebase/services/firebase-services.js" | relURL }}"></script>
<script src="{{ "js/firebase/services/auth-migration.js" | relURL }}"></script>

<!-- Firebase Queue Feature Modules -->
<script src="{{ "js/firebase/core/firebase-queue-metadata.js" | relURL }}"></script>
<script src="{{ "js/firebase/services/firebase-shared-queues.js" | relURL }}"></script>
<script src="{{ "js/firebase/services/firebase-personal-queues.js" | relURL }}"></script>
<script src="{{ "js/firebase/core/firebase-queue-storage.js" | relURL }}"></script>
<script src="{{ "js/firebase/core/firebase-queue.js" | relURL }}"></script>
<!-- Queue Management Components -->
<script src="{{ "js/ui/queue-management/display/queue-display.js" | relURL }}"></script>
<script src="{{ "js/ui/queue-management/controls/queue-controls.js" | relURL }}"></script>
<script src="{{ "js/ui/queue-management/items/queue-items.js" | relURL }}"></script>
<script src="{{ "js/ui/queue-management/actions/queue-actions.js" | relURL }}"></script>
<script src="{{ "js/ui/queue-management/scrolling/queue-scrolling.js" | relURL }}"></script>
<script src="{{ "js/ui/queue-management/draft/draft-queue.js" | relURL }}"></script>
<script src="{{ "js/ui/queue-management/sharing/queue-sharing.js" | relURL }}"></script>
<script src="{{ "js/ui/queue-management/index.js" | relURL }}"></script>

<!-- Personal Queues Components -->
<script src="{{ "js/ui/personal-queues/display/queue-display.js" | relURL }}"></script>
<script src="{{ "js/ui/personal-queues/privacy/queue-privacy.js" | relURL }}"></script>
<script src="{{ "js/ui/personal-queues/loading/queue-loading.js" | relURL }}"></script>
<script src="{{ "js/ui/personal-queues/actions/queue-actions.js" | relURL }}"></script>
<script src="{{ "js/ui/personal-queues/events/queue-events.js" | relURL }}"></script>
<script src="{{ "js/ui/personal-queues/utils/queue-utils.js" | relURL }}"></script>
<script src="{{ "js/ui/personal-queues/index.js" | relURL }}"></script>

<!-- Authentication UI -->
<script src="{{ "js/ui/auth/auth-ui.js" | relURL }}"></script>

<!-- Other UI Controllers -->
<script src="{{ "js/ui/queue-browser-controller.js" | relURL }}"></script>
<script src="{{ "js/ui/navigation-controller.js" | relURL }}"></script>
<script src="{{ "js/ui/ui-interactions-controller.js" | relURL }}"></script>
<script src="{{ "js/ui/enhanced-ux-controller.js" | relURL }}"></script>

<script>

  // Main application initialization function
  async function initializeApp() {
    console.log('🎵 YouTube Looper initializing...');

    try {
      // Initialize configuration
      await loadApiKey();

      // Initialize Firebase
      await initializeFirebase();

      // Initialize authentication UI
      if (typeof initializeAuthUI === 'function') {
        initializeAuthUI();
      }

      // Initialize YouTube API
      initializeYouTubeAPI();

      // Initialize enhanced UX features
      initializeEnhancedUX();

      // Initialize navigation
      initializeNavigation();

      // Initialize personal queues
      initializePersonalQueues();

      // Initialize UI interactions
      initializeUIInteractions();

      // Load saved queue from Firebase
      await loadQueueFromStorage();

      // Initialize UI state
      updateQueueDisplay();

      // Small delay to ensure header elements are ready
      setTimeout(() => {
        updatePlayerControls();
        console.log('🎛️ Player controls updated after initialization');
      }, 100);

      console.log('✅ YouTube Looper initialized successfully!');

    } catch (error) {
      console.error('❌ Error initializing YouTube Looper:', error);
      showMessage('Failed to initialize YouTube Looper. Please refresh the page.', 'error');
    }
  }

  // Initialize the application when DOM is ready
  document.addEventListener('DOMContentLoaded', async function() {
    await initializeApp();
  });

</script>
{{ end }}
