// ============================================================================
// AUTHENTICATION UI CONTROLLER
// ============================================================================

/**
 * Authentication UI state
 */
let authUIState = {
  isInitialized: false,
  currentUser: null,
  isAuthenticated: false
};

/**
 * Initialize authentication UI
 */
function initializeAuthUI() {
  console.log('🔐 Initializing authentication UI...');
  
  // Set up event listeners
  setupAuthEventListeners();
  
  // Set up global auth state listener
  document.addEventListener('authStateChanged', handleAuthStateChange);
  
  // Initialize UI state
  updateAuthUI();
  
  authUIState.isInitialized = true;
  console.log('✅ Authentication UI initialized');
}

/**
 * Set up authentication event listeners
 */
function setupAuthEventListeners() {
  // Sign in button
  const signInBtn = document.getElementById('auth-sign-in-btn');
  if (signInBtn) {
    signInBtn.addEventListener('click', handleSignIn);
  }
  
  // Sign out button
  const signOutBtn = document.getElementById('auth-sign-out-btn');
  if (signOutBtn) {
    signOutBtn.addEventListener('click', handleSignOut);
  }
  
  // User profile dropdown toggle
  const userProfileBtn = document.getElementById('auth-user-profile-btn');
  if (userProfileBtn) {
    userProfileBtn.addEventListener('click', toggleUserProfileDropdown);
  }
  
  // Close dropdown when clicking outside
  document.addEventListener('click', (event) => {
    const dropdown = document.getElementById('auth-user-dropdown');
    const profileBtn = document.getElementById('auth-user-profile-btn');
    
    if (dropdown && profileBtn && 
        !dropdown.contains(event.target) && 
        !profileBtn.contains(event.target)) {
      dropdown.classList.remove('show');
    }
  });
}

/**
 * Handle authentication state changes
 * @param {CustomEvent} event - Auth state change event
 */
function handleAuthStateChange(event) {
  const { isAuthenticated, user } = event.detail || {};
  
  authUIState.isAuthenticated = isAuthenticated;
  authUIState.currentUser = user;
  
  console.log('🔐 Auth UI state changed:', isAuthenticated, user?.uid);
  
  updateAuthUI();
}

/**
 * Handle sign in button click
 */
async function handleSignIn() {
  console.log('🔐 Sign in requested');

  const signInBtn = document.getElementById('auth-sign-in-btn');
  if (signInBtn) {
    signInBtn.disabled = true;
    signInBtn.innerHTML = `
      <div class="auth-loading">
        <div class="auth-spinner"></div>
        Signing in...
      </div>
    `;
  }

  try {
    // Check for migratable data before signing in
    let migrationData = null;
    if (window.authMigrationService) {
      migrationData = await window.authMigrationService.checkForMigratableData();
    }

    if (typeof signInWithGoogle === 'function') {
      const user = await signInWithGoogle();
      if (user) {
        console.log('✅ Sign in successful');

        // Handle data migration if needed
        if (migrationData && migrationData.hasMigratableData) {
          await handleDataMigration(user, migrationData);
        } else {
          showAuthNotification('Welcome! You are now signed in.', 'success');
        }
      } else {
        console.log('❌ Sign in cancelled or failed');
        showAuthNotification('Sign in was cancelled or failed. Please try again.', 'error');
      }
    } else {
      console.error('signInWithGoogle function not available');
      showAuthNotification('Authentication service not available.', 'error');
    }
  } catch (error) {
    console.error('❌ Sign in error:', error);
    showAuthNotification('Sign in failed. Please try again.', 'error');
  } finally {
    // Reset button state
    if (signInBtn) {
      signInBtn.disabled = false;
      updateSignInButton();
    }
  }
}

/**
 * Handle sign out button click
 */
async function handleSignOut() {
  console.log('🔓 Sign out requested');
  
  try {
    if (typeof signOut === 'function') {
      const success = await signOut();
      if (success) {
        console.log('✅ Sign out successful');
        showAuthNotification('You have been signed out.', 'info');
      } else {
        console.log('❌ Sign out failed');
        showAuthNotification('Sign out failed. Please try again.', 'error');
      }
    } else {
      console.error('signOut function not available');
    }
  } catch (error) {
    console.error('❌ Sign out error:', error);
    showAuthNotification('Sign out failed. Please try again.', 'error');
  }
}

/**
 * Toggle user profile dropdown
 */
function toggleUserProfileDropdown() {
  const dropdown = document.getElementById('auth-user-dropdown');
  if (dropdown) {
    dropdown.classList.toggle('show');
  }
}

/**
 * Update authentication UI based on current state
 */
function updateAuthUI() {
  updateSignInButton();
  updateUserProfile();
  updateAuthStatus();
}

/**
 * Update sign in button
 */
function updateSignInButton() {
  const signInBtn = document.getElementById('auth-sign-in-btn');
  if (!signInBtn) return;
  
  if (authUIState.isAuthenticated) {
    signInBtn.style.display = 'none';
  } else {
    signInBtn.style.display = 'flex';
    signInBtn.innerHTML = `
      <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
      </svg>
      Sign in with Google
    `;
  }
}

/**
 * Update user profile display
 */
function updateUserProfile() {
  const userProfileBtn = document.getElementById('auth-user-profile-btn');
  const userDropdown = document.getElementById('auth-user-dropdown');
  
  if (!userProfileBtn) return;
  
  if (authUIState.isAuthenticated && authUIState.currentUser) {
    const user = authUIState.currentUser;
    userProfileBtn.style.display = 'flex';
    
    // Update profile button
    userProfileBtn.innerHTML = `
      <div class="auth-user-avatar">
        ${user.photoURL ? 
          `<img src="${user.photoURL}" alt="Profile" class="auth-avatar-img">` :
          `<div class="auth-avatar-placeholder">${(user.displayName || user.email || 'U').charAt(0).toUpperCase()}</div>`
        }
      </div>
      <div class="auth-user-info">
        <div class="auth-user-name">${user.displayName || 'User'}</div>
        <div class="auth-user-email">${user.email || ''}</div>
      </div>
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="auth-dropdown-arrow">
        <path d="M7 10l5 5 5-5z"/>
      </svg>
    `;
    
    // Update dropdown content
    if (userDropdown) {
      userDropdown.innerHTML = `
        <div class="auth-dropdown-header">
          <div class="auth-dropdown-avatar">
            ${user.photoURL ? 
              `<img src="${user.photoURL}" alt="Profile" class="auth-avatar-img">` :
              `<div class="auth-avatar-placeholder">${(user.displayName || user.email || 'U').charAt(0).toUpperCase()}</div>`
            }
          </div>
          <div class="auth-dropdown-info">
            <div class="auth-dropdown-name">${user.displayName || 'User'}</div>
            <div class="auth-dropdown-email">${user.email || ''}</div>
          </div>
        </div>
        <div class="auth-dropdown-divider"></div>
        <button id="auth-sign-out-btn" class="auth-dropdown-item">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
          </svg>
          Sign out
        </button>
      `;
      
      // Re-attach sign out event listener
      const signOutBtn = userDropdown.querySelector('#auth-sign-out-btn');
      if (signOutBtn) {
        signOutBtn.addEventListener('click', handleSignOut);
      }
    }
  } else {
    userProfileBtn.style.display = 'none';
    if (userDropdown) {
      userDropdown.classList.remove('show');
    }
  }
}

/**
 * Update authentication status indicator
 */
function updateAuthStatus() {
  const statusIndicator = document.getElementById('auth-status-indicator');
  if (!statusIndicator) return;
  
  if (authUIState.isAuthenticated) {
    statusIndicator.className = 'auth-status-indicator authenticated';
    statusIndicator.title = 'Signed in - Your data is saved to your Google account';
  } else {
    statusIndicator.className = 'auth-status-indicator not-authenticated';
    statusIndicator.title = 'Not signed in - Sign in to save your queues permanently';
  }
}

/**
 * Handle data migration after successful sign-in
 * @param {Object} user - Google user object
 * @param {Object} migrationData - Migration data check result
 */
async function handleDataMigration(user, migrationData) {
  try {
    console.log('🔄 Handling data migration for', migrationData.totalQueues, 'items');

    // Show migration notification
    showAuthNotification(
      `Found ${migrationData.totalQueues} queue(s) from previous sessions. Migrating to your Google account...`,
      'info'
    );

    // Perform migration
    const migrationResult = await window.authMigrationService.migrateToGoogleAccount(user);

    if (migrationResult.success) {
      const totalMigrated = migrationResult.migratedQueues + migrationResult.migratedPersonalQueues;

      if (totalMigrated > 0) {
        showAuthNotification(
          `Successfully migrated ${totalMigrated} queue(s) to your Google account!`,
          'success'
        );

        // Clean up anonymous data
        await window.authMigrationService.cleanupAnonymousData();

        // Refresh personal queues if on that view
        if (typeof invalidatePersonalQueuesCache === 'function') {
          invalidatePersonalQueuesCache();
        }

        // Reload current view to show migrated data
        if (typeof loadPersonalQueues === 'function' && getCurrentView() === 'personal') {
          setTimeout(() => loadPersonalQueues(true), 1000);
        }
      } else {
        showAuthNotification('Welcome! You are now signed in.', 'success');
      }

      // Show any migration errors
      if (migrationResult.errors && migrationResult.errors.length > 0) {
        console.warn('Migration errors:', migrationResult.errors);
        showAuthNotification(
          `Some items could not be migrated: ${migrationResult.errors.join(', ')}`,
          'warning'
        );
      }
    } else {
      console.error('Migration failed:', migrationResult.error);
      showAuthNotification(
        'Welcome! You are signed in, but some data could not be migrated.',
        'warning'
      );
    }
  } catch (error) {
    console.error('❌ Migration handling error:', error);
    showAuthNotification(
      'Welcome! You are signed in, but there was an issue migrating your data.',
      'warning'
    );
  }
}

/**
 * Show authentication notification
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, info, warning)
 */
function showAuthNotification(message, type = 'info') {
  // Use existing notification system if available
  if (typeof showNotification === 'function') {
    showNotification(message, type);
  } else {
    console.log(`Auth notification (${type}):`, message);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeAuthUI);
} else {
  initializeAuthUI();
}

console.log('✅ Authentication UI module loaded');
