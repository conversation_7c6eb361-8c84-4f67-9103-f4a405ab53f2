// ============================================================================
// AUTHENTICATION MIGRATION SERVICE
// ============================================================================

/**
 * Migration service for transferring anonymous user data to Google account
 */
class AuthMigrationService {
  constructor() {
    this.migrationInProgress = false;
    this.migrationData = null;
  }

  /**
   * Check if user has anonymous data that can be migrated
   * @returns {Promise<Object>} Migration check result
   */
  async checkForMigratableData() {
    try {
      // Check for anonymous user data in localStorage
      const localData = this.getLocalStorageData();
      
      // Check for anonymous user data in Firebase
      const firebaseData = await this.getAnonymousFirebaseData();
      
      const hasMigratableData = localData.hasData || firebaseData.hasData;
      
      return {
        success: true,
        hasMigratableData,
        localData,
        firebaseData,
        totalQueues: (localData.queues?.length || 0) + (firebaseData.queues?.length || 0)
      };
    } catch (error) {
      console.error('❌ Error checking for migratable data:', error);
      return {
        success: false,
        error: error.message,
        hasMigratableData: false
      };
    }
  }

  /**
   * Get data from localStorage
   * @returns {Object} Local storage data
   */
  getLocalStorageData() {
    try {
      const data = {
        hasData: false,
        queues: [],
        currentQueue: null
      };

      // Check for saved queue data
      const savedQueue = localStorage.getItem('videoQueue');
      if (savedQueue) {
        try {
          const queueData = JSON.parse(savedQueue);
          if (queueData && queueData.length > 0) {
            data.hasData = true;
            data.currentQueue = {
              queue: queueData,
              title: 'Current Queue',
              timestamp: Date.now()
            };
          }
        } catch (e) {
          console.warn('Could not parse saved queue data:', e);
        }
      }

      return data;
    } catch (error) {
      console.warn('Could not access localStorage:', error);
      return { hasData: false, queues: [], currentQueue: null };
    }
  }

  /**
   * Get anonymous user data from Firebase
   * @returns {Promise<Object>} Firebase data
   */
  async getAnonymousFirebaseData() {
    try {
      const data = {
        hasData: false,
        queues: [],
        personalQueues: []
      };

      if (!isFirebaseInitialized()) {
        return data;
      }

      const auth = getFirebaseAuth();
      const currentUser = auth.currentUser;

      // Only check if user is anonymous
      if (!currentUser || !currentUser.isAnonymous) {
        return data;
      }

      const db = getFirebaseDb();
      const userId = currentUser.uid;

      // Check personal queues
      try {
        const personalQueuesRef = db.collection('personal_queues').where('userId', '==', userId);
        const personalSnapshot = await personalQueuesRef.get();
        
        if (!personalSnapshot.empty) {
          data.hasData = true;
          personalSnapshot.forEach(doc => {
            data.personalQueues.push({
              id: doc.id,
              ...doc.data()
            });
          });
        }
      } catch (error) {
        console.warn('Could not check personal queues:', error);
      }

      // Check public queues created by this user
      try {
        const publicQueuesRef = db.collection('queues').where('createdBy', '==', userId);
        const publicSnapshot = await publicQueuesRef.get();
        
        if (!publicSnapshot.empty) {
          data.hasData = true;
          publicSnapshot.forEach(doc => {
            data.queues.push({
              id: doc.id,
              ...doc.data()
            });
          });
        }
      } catch (error) {
        console.warn('Could not check public queues:', error);
      }

      return data;
    } catch (error) {
      console.error('Error getting anonymous Firebase data:', error);
      return { hasData: false, queues: [], personalQueues: [] };
    }
  }

  /**
   * Migrate anonymous data to Google account
   * @param {Object} googleUser - Google user object
   * @returns {Promise<Object>} Migration result
   */
  async migrateToGoogleAccount(googleUser) {
    if (this.migrationInProgress) {
      return { success: false, error: 'Migration already in progress' };
    }

    this.migrationInProgress = true;

    try {
      console.log('🔄 Starting data migration to Google account...');

      // Get migratable data
      const migrationCheck = await this.checkForMigratableData();
      if (!migrationCheck.success || !migrationCheck.hasMigratableData) {
        return { success: true, message: 'No data to migrate' };
      }

      const results = {
        success: true,
        migratedQueues: 0,
        migratedPersonalQueues: 0,
        errors: []
      };

      // Migrate current queue from localStorage
      if (migrationCheck.localData.currentQueue) {
        try {
          await this.migrateCurrentQueue(googleUser.uid, migrationCheck.localData.currentQueue);
          results.migratedQueues++;
        } catch (error) {
          console.error('Error migrating current queue:', error);
          results.errors.push('Failed to migrate current queue');
        }
      }

      // Migrate Firebase personal queues
      if (migrationCheck.firebaseData.personalQueues.length > 0) {
        for (const queue of migrationCheck.firebaseData.personalQueues) {
          try {
            await this.migratePersonalQueue(googleUser.uid, queue);
            results.migratedPersonalQueues++;
          } catch (error) {
            console.error('Error migrating personal queue:', error);
            results.errors.push(`Failed to migrate queue: ${queue.queueData?.title || 'Untitled'}`);
          }
        }
      }

      // Migrate Firebase public queues
      if (migrationCheck.firebaseData.queues.length > 0) {
        for (const queue of migrationCheck.firebaseData.queues) {
          try {
            await this.migratePublicQueue(googleUser.uid, queue);
            results.migratedQueues++;
          } catch (error) {
            console.error('Error migrating public queue:', error);
            results.errors.push(`Failed to migrate public queue: ${queue.title || 'Untitled'}`);
          }
        }
      }

      console.log('✅ Migration completed:', results);
      return results;

    } catch (error) {
      console.error('❌ Migration failed:', error);
      return {
        success: false,
        error: error.message
      };
    } finally {
      this.migrationInProgress = false;
    }
  }

  /**
   * Migrate current queue from localStorage
   * @param {string} googleUserId - Google user ID
   * @param {Object} queueData - Queue data to migrate
   */
  async migrateCurrentQueue(googleUserId, queueData) {
    if (!window.personalQueueRepo) {
      throw new Error('Personal queue repository not available');
    }

    // Create queue data model
    const queueDataModel = new QueueDataModel({
      [QueueDataModel.FIELDS.QUEUE]: queueData.queue,
      [QueueDataModel.FIELDS.CURRENT_INDEX]: 0,
      [QueueDataModel.FIELDS.IS_PLAYING]: false,
      [QueueDataModel.FIELDS.TIMESTAMP]: queueData.timestamp || Date.now(),
      [QueueDataModel.FIELDS.TITLE]: queueData.title || 'Migrated Queue'
    });

    // Generate new queue ID for Google user
    const queueId = PersonalQueueModel.generateQueueId(googleUserId);

    // Create personal queue model
    const personalQueue = new PersonalQueueModel({
      [PersonalQueueModel.FIELDS.ID]: queueId,
      [PersonalQueueModel.FIELDS.USER_ID]: googleUserId,
      [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueDataModel.toObject(),
      [PersonalQueueModel.FIELDS.IS_PERSONAL]: true,
      [PersonalQueueModel.FIELDS.IS_PUBLIC]: false
    });

    // Save to Firebase
    await window.personalQueueRepo.save(personalQueue);
    console.log('✅ Migrated current queue to Google account');
  }

  /**
   * Migrate personal queue
   * @param {string} googleUserId - Google user ID
   * @param {Object} queueData - Queue data to migrate
   */
  async migratePersonalQueue(googleUserId, queueData) {
    if (!window.personalQueueRepo) {
      throw new Error('Personal queue repository not available');
    }

    // Generate new queue ID for Google user
    const newQueueId = PersonalQueueModel.generateQueueId(googleUserId);

    // Update queue data with new user ID
    const updatedQueueData = {
      ...queueData,
      id: newQueueId,
      userId: googleUserId
    };

    // Create personal queue model
    const personalQueue = PersonalQueueModel.fromObject(updatedQueueData);

    // Save to Firebase
    await window.personalQueueRepo.save(personalQueue);
    console.log('✅ Migrated personal queue:', queueData.queueData?.title || 'Untitled');
  }

  /**
   * Migrate public queue
   * @param {string} googleUserId - Google user ID
   * @param {Object} queueData - Queue data to migrate
   */
  async migratePublicQueue(googleUserId, queueData) {
    if (!window.publicQueueRepo) {
      throw new Error('Public queue repository not available');
    }

    // Update queue data with new user ID
    const updatedQueueData = {
      ...queueData,
      createdBy: googleUserId
    };

    // Create public queue model
    const publicQueue = PublicQueueModel.fromObject(updatedQueueData);

    // Save to Firebase
    await window.publicQueueRepo.save(publicQueue);
    console.log('✅ Migrated public queue:', queueData.title || 'Untitled');
  }

  /**
   * Clean up anonymous data after successful migration
   * @returns {Promise<boolean>} Cleanup success
   */
  async cleanupAnonymousData() {
    try {
      console.log('🧹 Cleaning up anonymous data...');

      // Clear localStorage
      try {
        localStorage.removeItem('videoQueue');
        localStorage.removeItem('currentVideoIndex');
        localStorage.removeItem('isPlaying');
      } catch (error) {
        console.warn('Could not clear localStorage:', error);
      }

      console.log('✅ Anonymous data cleanup completed');
      return true;
    } catch (error) {
      console.error('❌ Error cleaning up anonymous data:', error);
      return false;
    }
  }
}

// Create global instance
window.authMigrationService = new AuthMigrationService();

console.log('✅ Authentication Migration Service loaded');
