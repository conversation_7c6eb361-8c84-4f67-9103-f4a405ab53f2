# Google Authentication Setup Guide

Follow these steps to enable Google authentication in your YouTube Looper application.

## Step 1: Firebase Console Configuration

### Enable Google Sign-In Provider

1. **Open Firebase Console**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Select your YouTube Looper project

2. **Navigate to Authentication**
   - Click "Authentication" in the left sidebar
   - Click "Sign-in method" tab

3. **Enable Google Provider**
   - Click on "Google" in the sign-in providers list
   - Toggle "Enable" to ON
   - Enter your project support email
   - Click "Save"

### Configure OAuth Consent Screen

1. **Go to Google Cloud Console**
   - Visit [Google Cloud Console](https://console.cloud.google.com/)
   - Select the same project as your Firebase project

2. **Configure OAuth Consent**
   - Navigate to "APIs & Services" > "OAuth consent screen"
   - Choose "External" user type (unless you have Google Workspace)
   - Fill in required fields:
     - App name: "Tubli - YouTube Looper"
     - User support email: Your email
     - Developer contact email: Your email
   - Add your domain to "Authorized domains"
   - Save and continue

3. **Add Scopes** (if prompted)
   - Add these scopes:
     - `../auth/userinfo.email`
     - `../auth/userinfo.profile`
   - Save and continue

### Add Authorized Domains

1. **In Firebase Console**
   - Go to Authentication > Settings > Authorized domains
   - Add your domains:
     - `localhost` (for development)
     - `tubli.to` (your production domain)
     - Any other domains you'll use

## Step 2: Test the Implementation

### Development Testing

1. **Start your development server**
   ```bash
   cd youtube-looper
   hugo server
   ```

2. **Open the application**
   - Navigate to `http://localhost:1313`
   - You should see the "Sign in with Google" button in the header

3. **Test sign-in flow**
   - Click the sign-in button
   - Google OAuth popup should appear
   - Grant permissions
   - You should be signed in with your profile visible

### Test Data Migration

1. **Create anonymous data**
   - Before signing in, create some queues
   - Add videos to the current queue

2. **Sign in with Google**
   - Click "Sign in with Google"
   - Complete the OAuth flow
   - You should see a notification about migrated data

3. **Verify migration**
   - Check that your queues are now in "My Queues"
   - Verify that your current queue is preserved

## Step 3: Production Deployment

### Update Firebase Configuration

1. **Add production domain**
   - In Firebase Console > Authentication > Settings
   - Add your production domain to authorized domains

2. **Update OAuth consent screen**
   - Add production domain to authorized domains
   - Verify all settings are correct

### Deploy and Test

1. **Deploy your application**
   - Upload files to your hosting provider
   - Ensure all JavaScript and CSS files are accessible

2. **Test production authentication**
   - Visit your production site
   - Test the complete sign-in flow
   - Verify data persistence across browser sessions

## Troubleshooting

### Common Issues

**"This app isn't verified" warning**
- This is normal for new applications
- Users can click "Advanced" > "Go to [your app] (unsafe)"
- To remove this warning, submit your app for verification in Google Cloud Console

**Sign-in popup blocked**
- Ensure popup blockers are disabled
- The application handles this error gracefully

**"Unauthorized domain" error**
- Verify your domain is added to Firebase authorized domains
- Check that the domain matches exactly (including www/non-www)

**Migration not working**
- Check browser console for errors
- Verify Firebase rules allow authenticated users to write
- Ensure migration service is loaded before authentication

### Debug Steps

1. **Check browser console**
   - Look for authentication-related errors
   - Verify Firebase initialization messages

2. **Verify Firebase configuration**
   - Ensure `firebase-config.json` has correct project settings
   - Check that all required Firebase services are enabled

3. **Test with different browsers**
   - Try incognito/private browsing mode
   - Test with different Google accounts

## Security Considerations

### Production Security

1. **Review Firestore Rules**
   - Ensure rules properly restrict access to user's own data
   - Test rules with Firebase Rules Playground

2. **Monitor Authentication**
   - Set up Firebase Analytics to monitor sign-in events
   - Watch for unusual authentication patterns

3. **Regular Updates**
   - Keep Firebase SDK updated
   - Monitor Google OAuth security announcements

### Privacy Compliance

1. **Privacy Policy**
   - Update your privacy policy to mention Google sign-in
   - Explain what data is collected and how it's used

2. **Data Handling**
   - Only request necessary scopes (email, profile)
   - Implement data deletion if users request it

## Support

If you encounter issues:

1. **Check Firebase Console**
   - Look at Authentication logs
   - Check Firestore usage and errors

2. **Google Cloud Console**
   - Review OAuth consent screen status
   - Check API quotas and limits

3. **Application Logs**
   - Monitor browser console for JavaScript errors
   - Check network requests for failed API calls

## Success Indicators

You'll know the setup is working when:

✅ Sign-in button appears in header  
✅ Google OAuth popup opens when clicked  
✅ User profile appears after successful sign-in  
✅ Personal queues load for authenticated users  
✅ Data migration works for existing anonymous users  
✅ Sign-out functionality works correctly  
✅ Authentication persists across browser sessions  

## Next Steps

After successful setup:

1. **Monitor Usage**
   - Track authentication metrics in Firebase Analytics
   - Monitor user engagement with personal queues

2. **Gather Feedback**
   - Ask users about the sign-in experience
   - Monitor for any authentication-related issues

3. **Consider Enhancements**
   - Add profile management features
   - Implement social sharing capabilities
   - Consider additional authentication providers
